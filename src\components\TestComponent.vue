
<template>
    <div class="card">
        <DataTable v-model:expandedRowGroups="expandedRowGroups" :value="customers" tableStyle="min-width: 50rem"
                expandableRowGroups rowGroupMode="subheader" groupRowsBy="representative.name" @rowgroup-expand="onRowGroupExpand" @rowgroup-collapse="onRowGroupCollapse"
                sortMode="single" sortField="representative.name" :sortOrder="1">
            <template #groupheader="slotProps">
                <img :alt="slotProps.data.representative.name" :src="`https://primefaces.org/cdn/primevue/images/avatar/${slotProps.data.representative.image}`" width="32" style="vertical-align: middle; display: inline-block" class="ml-2" />
                <span class="align-middle ml-2 font-bold leading-normal">{{ slotProps.data.representative.name }}</span>
            </template>
            <Column field="representative.name" header="Representative"></Column>
            <Column field="name" header="Name" style="width: 20%"></Column>
            <Column field="country" header="Country" style="width: 20%">
                <template #body="slotProps">
                    <div class="flex items-center gap-2">
                        <img alt="flag" src="https://primefaces.org/cdn/primevue/images/flag/flag_placeholder.png" :class="`flag flag-${slotProps.data.country.code}`" style="width: 24px" />
                        <span>{{ slotProps.data.country.name }}</span>
                    </div>
                </template>
            </Column>
            <Column field="company" header="Company" style="width: 20%"></Column>
            <Column field="status" header="Status" style="width: 20%">
                <template #body="slotProps">
                    <Tag :value="slotProps.data.status" :severity="getSeverity(slotProps.data.status)" />
                </template>
            </Column>
            <Column field="date" header="Date" style="width: 20%"></Column>
            <template #groupfooter="slotProps">
                <div class="flex justify-end font-bold w-full">Total Customers: {{ calculateCustomerTotal(slotProps.data.representative.name) }}</div>
            </template>
        </DataTable>
        <Toast />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';

// 静态模拟数据
const mockCustomers = [
    {
        id: 1000,
        name: 'James Butt',
        country: { name: 'Algeria', code: 'dz' },
        company: 'Benton, John B Jr',
        date: '2015-09-13',
        status: 'unqualified',
        representative: { name: 'Amy Elsner', image: 'amyelsner.png' }
    },
    {
        id: 1001,
        name: 'Josephine Darakjy',
        country: { name: 'Egypt', code: 'eg' },
        company: 'Chanay, Jeffrey A Esq',
        date: '2019-02-09',
        status: 'proposal',
        representative: { name: 'Amy Elsner', image: 'amyelsner.png' }
    },
    {
        id: 1002,
        name: 'Art Venere',
        country: { name: 'Panama', code: 'pa' },
        company: 'Chemel, James L Cpa',
        date: '2017-05-13',
        status: 'qualified',
        representative: { name: 'Asiya Javayant', image: 'asiyajavayant.png' }
    },
    {
        id: 1003,
        name: 'Lenna Paprocki',
        country: { name: 'Slovenia', code: 'si' },
        company: 'Feltz Printing Service',
        date: '2020-09-15',
        status: 'new',
        representative: { name: 'Asiya Javayant', image: 'asiyajavayant.png' }
    },
    {
        id: 1004,
        name: 'Donette Foller',
        country: { name: 'South Africa', code: 'za' },
        company: 'Printing Dimensions',
        date: '2016-05-20',
        status: 'proposal',
        representative: { name: 'Onyama Limba', image: 'onyamalimba.png' }
    },
    {
        id: 1005,
        name: 'Simona Morasca',
        country: { name: 'Egypt', code: 'eg' },
        company: 'Chapman, Ross E Esq',
        date: '2018-02-16',
        status: 'qualified',
        representative: { name: 'Onyama Limba', image: 'onyamalimba.png' }
    },
    {
        id: 1006,
        name: 'Mitsue Tollner',
        country: { name: 'Paraguay', code: 'py' },
        company: 'Morlong Associates',
        date: '2018-02-19',
        status: 'renewal',
        representative: { name: 'Ivan Magalhaes', image: 'ivanmagalhaes.png' }
    },
    {
        id: 1007,
        name: 'Leota Dilliard',
        country: { name: 'Serbia', code: 'rs' },
        company: 'Commercial Press',
        date: '2019-08-13',
        status: 'renewal',
        representative: { name: 'Ivan Magalhaes', image: 'ivanmagalhaes.png' }
    },
    {
        id: 1008,
        name: 'Sage Wieser',
        country: { name: 'Egypt', code: 'eg' },
        company: 'Truhlar And Truhlar Attys',
        date: '2018-11-21',
        status: 'unqualified',
        representative: { name: 'Stephen Shaw', image: 'stephenshaw.png' }
    },
    {
        id: 1009,
        name: 'Kris Marrier',
        country: { name: 'Mexico', code: 'mx' },
        company: 'King, Christopher A Esq',
        date: '2015-07-07',
        status: 'proposal',
        representative: { name: 'Stephen Shaw', image: 'stephenshaw.png' }
    }
];

onMounted(() => {
    // 模拟异步加载数据
    setTimeout(() => {
        customers.value = mockCustomers;
    }, 100);
});

const customers = ref();
const expandedRowGroups = ref();
const toast = useToast();
const onRowGroupExpand = (event) => {
    toast.add({ severity: 'info', summary: 'Row Group Expanded', detail: 'Value: ' + event.data, life: 3000 });
};
const onRowGroupCollapse = (event) => {
    toast.add({ severity: 'success', summary: 'Row Group Collapsed', detail: 'Value: ' + event.data, life: 3000 });
};
const calculateCustomerTotal = (name) => {
    let total = 0;

    if (customers.value) {
        for (let customer of customers.value) {
            if (customer.representative.name === name) {
                total++;
            }
        }
    }

    return total;
};
const getSeverity = (status) => {
    switch (status) {
        case 'unqualified':
            return 'danger';

        case 'qualified':
            return 'success';

        case 'new':
            return 'info';

        case 'negotiation':
            return 'warn';

        case 'renewal':
            return null;
    }
};
</script>
