<template>
    <div class="sidebar">
        <div class="logo">
            <i class="pi pi-prime" style="font-size: 1.6rem"></i>
        </div>
        <div class="operation">
            <Button class="button1 button" icon="pi pi-home" style="font-size: 1rem;background-color: #202020;"
                @click="setFunction('home')"/>


            <Button class="button2 button" icon="pi pi-cloud-upload" style="font-size: 1rem;background-color: #909090;"
                @click="setFunction('upload')"/>


        </div>
    </div>
</template>

<script setup>
import Button from 'primevue/button'
import { inject } from 'vue';

const sideBar_Function_Activated = inject('sideBar_Function_Activated');

// 设置侧边栏功能状态的函数
const setFunction = (functionName) => {
    sideBar_Function_Activated.value = functionName;
};

</script>

<style>
@layer reset,primevue,custom;

@layer custom {
    .sidebar {
        display: flex;
        flex-direction: column;
        align-items: center;
        /* justify-content: center; */
        height: 100vh;
        width: 4rem;
        background-color: #f8fafc;
        padding: 1rem;
        box-sizing: border-box;
        border-radius: 20px;
    }

    .sidebar .logo {
        display: flex;
        align-items: center;
        justify-content: center;

        width: 2.5rem;
        height: 2.5rem;

        margin-bottom: 2.2rem;
        margin-top: 0.5rem;
        
        border: 1px solid #202020;
        border-radius: 20%;
    }


    .sidebar .operation {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        width: 100%;
    }

    .sidebar .button {
        width: 2.6rem;
        height: 2.6rem;
        /* background-color: #202020; */
        border-radius: 50%;
        border: none;
    }
}
</style>