(function(){"use strict";var e={8716:function(e,a,t){var l=t(5130),o=t(6768),r=t(144),d=t(9018);const i={class:"sidebar"},s={class:"operation"};var u={__name:"SideBar",setup(e){const a=(0,o.WQ)("sideBar_Function_Activated"),t=e=>{a.value=e};return(e,a)=>((0,o.uX)(),(0,o.CE)("div",i,[a[2]||(a[2]=(0,o.Lk)("div",{class:"logo"},[(0,o.Lk)("i",{class:"pi pi-prime",style:{"font-size":"1.6rem"}})],-1)),(0,o.Lk)("div",s,[(0,o.bF)((0,r.R1)(d.A),{class:"button1 button",icon:"pi pi-home",style:{"font-size":"1rem","background-color":"#202020"},onClick:a[0]||(a[0]=e=>t("home"))}),(0,o.bF)((0,r.R1)(d.A),{class:"button2 button",icon:"pi pi-cloud-upload",style:{"font-size":"1rem","background-color":"#909090"},onClick:a[1]||(a[1]=e=>t("upload"))})])]))}};const n=u;var f=n,c=t(4232);const p={class:"main-operation-area-container"},F={class:"title"},v={class:"operation"},b={key:0,class:"user-info"},m={class:"user-name"},h={class:"user-role"};var y={__name:"MainOperationArea",setup(e){const a=(0,o.WQ)("mainOperationArea_Title"),t=(0,o.WQ)("currentUser"),l=e=>{const a={admin:"管理员",user:"普通用户",demo:"演示用户"};return a[e]||e};return(e,i)=>((0,o.uX)(),(0,o.CE)("div",p,[(0,o.Lk)("div",F,(0,c.v_)((0,r.R1)(a)),1),(0,o.Lk)("div",v,[(0,r.R1)(t)?((0,o.uX)(),(0,o.CE)("div",b,[(0,o.Lk)("span",m,(0,c.v_)((0,r.R1)(t).name),1),(0,o.Lk)("span",h,"("+(0,c.v_)(l((0,r.R1)(t).role))+")",1)])):(0,o.Q3)("",!0),(0,o.bF)((0,r.R1)(d.A),{class:"download-button",label:"下载",icon:"pi pi-download",style:{"font-size":"1rem"}})])]))}};const S=y;var R=S,k=t(7727),A=t(5664),Y=t(6966),_=t(2574),x=t(8778),E=t(2816),g=t(373),C=t(4392),L=t(1838),q=t(4709);const z={class:"main-periodCompareDataBoard-container"},w={class:"card-header"},N={class:"right"},W={class:"options-panel-content"};var V={__name:"PeriodCompareDataBoard",setup(e){const a=(0,r.KR)(),t=(0,r.KR)(),l=(0,r.KR)({name:"新增客户数",code:"ACUS"}),i=(0,r.KR)([{name:"新增客户数",code:"ACUS"},{name:"新增权益（仅入金）",code:"APROF"},{name:"新增净权益（入金-出金）",code:"ANPROF"},{name:"新增手续费",code:"AST"}]),s=(0,o.EW)(()=>{let e=0;for(let a of F.value)e+=a.lastYearSale;return parseFloat(e.toFixed(2))}),u=(0,o.EW)(()=>{let e=0;for(let a of F.value)e+=a.thisYearSale;return parseFloat(e.toFixed(2))}),n=(0,o.EW)(()=>{let e=0;for(let a of F.value)e+=a.diff;return parseFloat(e.toFixed(2))}),f=e=>e<0?"red-text":"green-text",p=()=>{a.value=null,t.value=null},F=(0,o.EW)(()=>{let e=[];return"ACUS"===l.value?.code?e=[{product:"林砚秋",lastYearSale:10,thisYearSale:17,diff:-7},{product:"顾明宇",lastYearSale:5,thisYearSale:10,diff:-5},{product:"苏晚柠",lastYearSale:38,thisYearSale:5,diff:33},{product:"陈景行",lastYearSale:7,thisYearSale:7,diff:0},{product:"孟星辞",lastYearSale:17,thisYearSale:10,diff:7},{product:"周砚舟",lastYearSale:29,thisYearSale:33,diff:-4},{product:"周砚舟",lastYearSale:10,thisYearSale:7,diff:3},{product:"夏知遥",lastYearSale:27,thisYearSale:33,diff:-6},{product:"陆承宇",lastYearSale:22,thisYearSale:11,diff:11},{product:"沈叙白",lastYearSale:51,thisYearSale:12,diff:39}]:"APROF"===l.value?.code?e=[{product:"林砚秋",lastYearSale:12500.5,thisYearSale:17800.8,diff:parseFloat((12500.5-17800.8).toFixed(2))},{product:"顾明宇",lastYearSale:5200.2,thisYearSale:10350.35,diff:parseFloat((5200.2-10350.35).toFixed(2))},{product:"苏晚柠",lastYearSale:38600.6,thisYearSale:5750.75,diff:parseFloat(32849.85.toFixed(2))},{product:"陈景行",lastYearSale:71000.1,thisYearSale:71000.1,diff:parseFloat((0).toFixed(2))},{product:"孟星辞",lastYearSale:17900.9,thisYearSale:10250.25,diff:parseFloat((17900.9-10250.25).toFixed(2))},{product:"周砚舟",lastYearSale:294000.4,thisYearSale:336500.65,diff:parseFloat((-42500.25).toFixed(2))},{product:"周砚舟",lastYearSale:10800.8,thisYearSale:7500.5,diff:parseFloat((10800.8-7500.5).toFixed(2))},{product:"夏知遥",lastYearSale:27300.3,thisYearSale:33900.9,diff:parseFloat((27300.3-33900.9).toFixed(2))},{product:"陆承宇",lastYearSale:226000.6,thisYearSale:114500.45,diff:parseFloat(111500.15000000001.toFixed(2))},{product:"沈叙白",lastYearSale:512500.25,thisYearSale:128000.8,diff:parseFloat(384499.45.toFixed(2))}]:"ANPROF"===l.value?.code?e=[{product:"林砚秋",lastYearSale:45200.75,thisYearSale:52800.4,diff:parseFloat((45200.75-52800.4).toFixed(2))},{product:"顾明宇",lastYearSale:89600.3,thisYearSale:75300.65,diff:parseFloat((89600.3-75300.65).toFixed(2))},{product:"苏晚柠",lastYearSale:125800.9,thisYearSale:142600.2,diff:parseFloat((125800.9-142600.2).toFixed(2))},{product:"陈景行",lastYearSale:36400.15,thisYearSale:39800.8,diff:parseFloat((36400.15-39800.8).toFixed(2))},{product:"孟星辞",lastYearSale:215700.5,thisYearSale:198300.75,diff:parseFloat(17399.75.toFixed(2))},{product:"周砚舟",lastYearSale:68900.4,thisYearSale:82500.3,diff:parseFloat((68900.4-82500.3).toFixed(2))},{product:"周砚舟",lastYearSale:156200.85,thisYearSale:149700.6,diff:parseFloat(6500.25.toFixed(2))},{product:"夏知遥",lastYearSale:94300.25,thisYearSale:108600.5,diff:parseFloat((-14300.25).toFixed(2))},{product:"陆承宇",lastYearSale:287500.6,thisYearSale:312800.9,diff:parseFloat((287500.6-312800.9).toFixed(2))},{product:"沈叙白",lastYearSale:421900.75,thisYearSale:398600.4,diff:parseFloat(23300.349999999977.toFixed(2))}]:"AST"===l.value?.code&&(e=[{product:"林砚秋",lastYearSale:67800.25,thisYearSale:72500.6,diff:parseFloat((67800.25-72500.6).toFixed(2))},{product:"顾明宇",lastYearSale:135200.8,thisYearSale:129800.45,diff:parseFloat(5400.349999999991.toFixed(2))},{product:"苏晚柠",lastYearSale:52300.7,thisYearSale:68900.9,diff:parseFloat((52300.7-68900.9).toFixed(2))},{product:"陈景行",lastYearSale:218500.3,thisYearSale:205700.65,diff:parseFloat(12799.649999999994.toFixed(2))},{product:"孟星辞",lastYearSale:89600.5,thisYearSale:94200.2,diff:parseFloat((89600.5-94200.2).toFixed(2))},{product:"周砚舟",lastYearSale:342700.85,thisYearSale:368900.5,diff:parseFloat((342700.85-368900.5).toFixed(2))},{product:"周砚舟",lastYearSale:76400.15,thisYearSale:71200.8,diff:parseFloat((76400.15-71200.8).toFixed(2))},{product:"夏知遥",lastYearSale:189300.6,thisYearSale:201500.3,diff:parseFloat((189300.6-201500.3).toFixed(2))},{product:"陆承宇",lastYearSale:95700.4,thisYearSale:88900.75,diff:parseFloat((95700.4-88900.75).toFixed(2))},{product:"沈叙白",lastYearSale:486200.9,thisYearSale:512700.25,diff:parseFloat((486200.9-512700.25).toFixed(2))}]),e});return(e,v)=>((0,o.uX)(),(0,o.CE)("div",z,[(0,o.bF)((0,r.R1)(k.A),null,{header:(0,o.k6)(()=>[(0,o.Lk)("div",w,[v[5]||(v[5]=(0,o.Lk)("div",{class:"left"},[(0,o.Lk)("span",{class:"card-title",style:{"z-index":"10"}},"周期业务数据对比")],-1)),(0,o.Lk)("div",N,[(0,o.bF)((0,r.R1)(A.A),{variant:"on"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(Y.A),{modelValue:a.value,"onUpdate:modelValue":v[0]||(v[0]=e=>a.value=e),inputId:"on_label",showIcon:"",iconDisplay:"input",selectionMode:"range",manualInput:!1,size:"small"},null,8,["modelValue"]),v[3]||(v[3]=(0,o.Lk)("label",{for:"on_label"},"周期1",-1))]),_:1,__:[3]}),(0,o.bF)((0,r.R1)(A.A),{variant:"on"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(Y.A),{modelValue:t.value,"onUpdate:modelValue":v[1]||(v[1]=e=>t.value=e),inputId:"on_label",showIcon:"",iconDisplay:"input",selectionMode:"range",manualInput:!1,size:"small"},null,8,["modelValue"]),v[4]||(v[4]=(0,o.Lk)("label",{for:"on_label"},"周期2",-1))]),_:1,__:[4]}),(0,o.bF)((0,r.R1)(d.A),{class:"download-button",icon:"pi pi-undo",style:{"font-size":"1rem"},size:"small",onClick:p}),(0,o.bF)((0,r.R1)(d.A),{class:"download-button",icon:"pi pi-search",style:{"font-size":"1rem"},size:"small"})])])]),content:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(_.A),{style:{height:"27rem"}},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(x.A),{class:"options-panel",size:25,minSize:10},{default:(0,o.k6)(()=>[v[6]||(v[6]=(0,o.Lk)("div",{class:"info"},[(0,o.Lk)("p",null,"请选择要查看的业务数据项")],-1)),(0,o.Lk)("div",W,[(0,o.bF)((0,r.R1)(q.A),{modelValue:l.value,"onUpdate:modelValue":v[2]||(v[2]=e=>l.value=e),options:i.value,optionLabel:"name",class:"listbox"},null,8,["modelValue","options"])])]),_:1,__:[6]}),(0,o.bF)((0,r.R1)(x.A),{class:"data-panel",size:75},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(E.A),{value:F.value,stripedRows:"",tableStyle:"min-width: 50rem",scrollable:"",scrollHeight:"27rem"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(C.A),{type:"header"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(L.A),null,{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(g.A),{header:"业务人员",rowspan:3})]),_:1}),(0,o.bF)((0,r.R1)(L.A),null,{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(g.A),{header:l.value.name,colspan:4},null,8,["header"])]),_:1}),(0,o.bF)((0,r.R1)(L.A),null,{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(g.A),{header:"周期1",sortable:"",field:"lastYearSale"}),(0,o.bF)((0,r.R1)(g.A),{header:"周期2",sortable:"",field:"thisYearSale"}),(0,o.bF)((0,r.R1)(g.A),{header:"差值（周期1-周期2）",sortable:"",field:"diff"})]),_:1})]),_:1}),(0,o.bF)((0,r.R1)(g.A),{field:"product"}),(0,o.bF)((0,r.R1)(g.A),{field:"lastYearSale"}),(0,o.bF)((0,r.R1)(g.A),{field:"thisYearSale"}),(0,o.bF)((0,r.R1)(g.A),{field:"diff"},{body:(0,o.k6)(e=>[(0,o.Lk)("span",{class:(0,c.C4)(f(e.data.diff))},(0,c.v_)(e.data.diff),3)]),_:1}),(0,o.bF)((0,r.R1)(C.A),{type:"footer"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(L.A),null,{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(g.A),{footer:"总计:"}),(0,o.bF)((0,r.R1)(g.A),{footer:s.value,footerStyle:"text-align:left"},null,8,["footer"]),(0,o.bF)((0,r.R1)(g.A),{footer:u.value,footerStyle:"text-align:left"},null,8,["footer"]),(0,o.bF)((0,r.R1)(g.A),{footer:n.value,footerStyle:"text-align:left"},null,8,["footer"])]),_:1})]),_:1})]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})]))}};const O=V;var B=O,U=(t(8111),t(7588),t(9675));const X={class:"main-periodCompareDataBoard-container"},K={class:"card-header"},D={class:"right"};var I={__name:"DataBoard",setup(e){const a=(0,r.KR)(),t=(0,r.KR)(),l=(0,r.KR)([{product:"林砚秋",addCustomer:10,totalCustomer:108,addEquity:-1592611.99,addNetEquity:6685099.85,totalEquity:7885099.85,addFee:680075.23,totalFee:1310075.23},{product:"顾明宇",addCustomer:6,totalCustomer:86,addEquity:987245.32,addNetEquity:4123560.78,totalEquity:5210806.1,addFee:421560.35,totalFee:832750.67},{product:"苏晚柠",addCustomer:26,totalCustomer:156,addEquity:2156890.45,addNetEquity:9236540.21,totalEquity:11393430.66,addFee:942870.56,totalFee:1856320.89},{product:"陈景行",addCustomer:7,totalCustomer:95,addEquity:-876540.23,addNetEquity:3689210.75,totalEquity:4565750.98,addFee:378540.62,totalFee:742150.38},{product:"孟星辞",addCustomer:13,totalCustomer:120,addEquity:-1356890.76,addNetEquity:5689210.34,totalEquity:7046101.1,addFee:582140.75,totalFee:1143680.92},{product:"周砚舟",addCustomer:23,totalCustomer:145,addEquity:1987650.43,addNetEquity:8326540.91,totalEquity:10314191.34,addFee:853260.47,totalFee:1678420.63},{product:"夏知遥",addCustomer:2,totalCustomer:68,addEquity:-456210.89,addNetEquity:1897650.32,totalEquity:2353861.21,addFee:195320.68,totalFee:382650.45},{product:"陆承宇",addCustomer:9,totalCustomer:102,addEquity:1245780.65,addNetEquity:5236890.47,totalEquity:6482671.12,addFee:536240.89,totalFee:1053680.74},{product:"江念初",addCustomer:15,totalCustomer:128,addEquity:1568920.37,addNetEquity:6547890.23,totalEquity:8116810.6,addFee:672450.32,totalFee:1321680.59},{product:"沈叙白",addCustomer:10,totalCustomer:112,addEquity:1423560.89,addNetEquity:5987650.43,totalEquity:7411211.32,addFee:612580.47,totalFee:1198750.63}]),i=(0,r.KR)(),s=(0,r.KR)([{name:"近1周",code:"NY"},{name:"近1月",code:"RM"},{name:"近3月",code:"LDN"}]),u=(0,o.EW)(()=>null!==i.value&&void 0!==i.value);(0,o.wB)(i,e=>{e&&(a.value=null,t.value=null)});const n=()=>{i.value=null,a.value=null,t.value=null},f=(0,o.EW)(()=>{let e=0;return l.value.forEach(a=>{e+=a.addCustomer}),parseFloat(e.toFixed(2))}),c=(0,o.EW)(()=>{let e=0;return l.value.forEach(a=>{e+=a.totalCustomer}),parseFloat(e.toFixed(2))}),p=(0,o.EW)(()=>{let e=0;return l.value.forEach(a=>{e+=a.addEquity}),parseFloat(e.toFixed(2))}),F=(0,o.EW)(()=>{let e=0;return l.value.forEach(a=>{e+=a.addNetEquity}),parseFloat(e.toFixed(2))}),v=(0,o.EW)(()=>{let e=0;return l.value.forEach(a=>{e+=a.totalEquity}),parseFloat(e.toFixed(2))}),b=(0,o.EW)(()=>{let e=0;return l.value.forEach(a=>{e+=a.addFee}),parseFloat(e.toFixed(2))}),m=(0,o.EW)(()=>{let e=0;return l.value.forEach(a=>{e+=a.totalFee}),parseFloat(e.toFixed(2))});return(e,h)=>((0,o.uX)(),(0,o.CE)("div",X,[(0,o.bF)((0,r.R1)(k.A),{class:"main-periodCompareDataBoard-card"},{header:(0,o.k6)(()=>[(0,o.Lk)("div",K,[h[5]||(h[5]=(0,o.Lk)("div",{class:"left"},[(0,o.Lk)("span",{class:"card-title",style:{"z-index":"10"}},"周期业务数据明细")],-1)),(0,o.Lk)("div",D,[(0,o.bF)((0,r.R1)(U.A),{modelValue:i.value,"onUpdate:modelValue":h[0]||(h[0]=e=>i.value=e),options:s.value,optionLabel:"name",placeholder:"快捷区间查询",class:"w-full md:w-56",size:"small"},null,8,["modelValue","options"]),(0,o.bF)((0,r.R1)(A.A),{variant:"on"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(Y.A),{modelValue:a.value,"onUpdate:modelValue":h[1]||(h[1]=e=>a.value=e),inputId:"start_date",showIcon:"",iconDisplay:"input",size:"small",disabled:u.value},null,8,["modelValue","disabled"]),h[3]||(h[3]=(0,o.Lk)("label",{for:"start_date"},"开始时间",-1))]),_:1,__:[3]}),(0,o.bF)((0,r.R1)(A.A),{variant:"on"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(Y.A),{modelValue:t.value,"onUpdate:modelValue":h[2]||(h[2]=e=>t.value=e),inputId:"end_date",showIcon:"",iconDisplay:"input",size:"small",disabled:u.value},null,8,["modelValue","disabled"]),h[4]||(h[4]=(0,o.Lk)("label",{for:"end_date"},"结束时间",-1))]),_:1,__:[4]}),(0,o.bF)((0,r.R1)(d.A),{class:"download-button",icon:"pi pi-undo",style:{"font-size":"1rem"},size:"small",onClick:n}),(0,o.bF)((0,r.R1)(d.A),{class:"download-button",icon:"pi pi-search",style:{"font-size":"1rem"},size:"small"})])])]),content:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(E.A),{value:l.value,stripedRows:"",tableStyle:"min-width: 50rem",scrollable:"",scrollHeight:"27rem"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(C.A),{type:"header"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(L.A),null,{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(g.A),{header:"业务人员"}),(0,o.bF)((0,r.R1)(g.A),{header:"新增客户数",sortable:"",field:"addCustomer"}),(0,o.bF)((0,r.R1)(g.A),{header:"总客户数",sortable:"",field:"totalCustomer"}),(0,o.bF)((0,r.R1)(g.A),{header:"新增权益（仅入金）",sortable:"",field:"addEquity"}),(0,o.bF)((0,r.R1)(g.A),{header:"新增净权益（入金-出金）",sortable:"",field:"addNetEquity"}),(0,o.bF)((0,r.R1)(g.A),{header:"总权益",sortable:"",field:"totalEquity"}),(0,o.bF)((0,r.R1)(g.A),{header:"新增手续费",sortable:"",field:"addFee"}),(0,o.bF)((0,r.R1)(g.A),{header:"总手续费",sortable:"",field:"totalFee"})]),_:1})]),_:1}),(0,o.bF)((0,r.R1)(g.A),{field:"product",header:"业务人员"}),(0,o.bF)((0,r.R1)(g.A),{field:"addCustomer",header:"新增客户数"}),(0,o.bF)((0,r.R1)(g.A),{field:"totalCustomer",header:"总客户数"}),(0,o.bF)((0,r.R1)(g.A),{field:"addEquity",header:"新增权益（仅入金）"}),(0,o.bF)((0,r.R1)(g.A),{field:"addNetEquity",header:"新增净权益（入金-出金）"}),(0,o.bF)((0,r.R1)(g.A),{field:"totalEquity",header:"总权益"}),(0,o.bF)((0,r.R1)(g.A),{field:"addFee",header:"新增手续费"}),(0,o.bF)((0,r.R1)(g.A),{field:"totalFee",header:"总手续费"}),(0,o.bF)((0,r.R1)(C.A),{type:"footer"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(L.A),null,{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(g.A),{footer:"总计:",footerStyle:"text-align: left;"}),(0,o.bF)((0,r.R1)(g.A),{footer:f.value,footerStyle:"text-align: left;"},null,8,["footer"]),(0,o.bF)((0,r.R1)(g.A),{footer:c.value,footerStyle:"text-align: left;"},null,8,["footer"]),(0,o.bF)((0,r.R1)(g.A),{footer:p.value,footerStyle:"text-align: left;"},null,8,["footer"]),(0,o.bF)((0,r.R1)(g.A),{footer:F.value,footerStyle:"text-align: left;"},null,8,["footer"]),(0,o.bF)((0,r.R1)(g.A),{footer:v.value,footerStyle:"text-align: left;"},null,8,["footer"]),(0,o.bF)((0,r.R1)(g.A),{footer:b.value,footerStyle:"text-align: left;"},null,8,["footer"]),(0,o.bF)((0,r.R1)(g.A),{footer:m.value,footerStyle:"text-align: left;"},null,8,["footer"])]),_:1})]),_:1})]),_:1},8,["value"])]),_:1})]))}};const M=I;var Q=M,P=t(1755),T=t(3448),j=t(3809);const G={class:"upload-board-container"},H={key:0},J={class:"file-info"},Z={class:"file-details"},$={class:"file-name"},ee={class:"file-size"},ae={key:0,class:"upload-progress"},te={style:{"font-size":"0.7rem","line-height":"1"}},le={class:"upload-status"},oe={key:0,style:{"margin-top":"2rem"}},re={style:{display:"flex","align-items":"center",gap:"0.5rem"}};var de={__name:"UpLoadBoard",setup(e){const a=(0,r.KR)({}),t=(0,r.KR)([]),l=async e=>{const a=e.files;for(let t of a)await i(t)},i=async e=>{const l=e.name;a.value[l]={progress:0,status:"准备上传...",uploading:!0};try{for(let e=0;e<=100;e+=10)a.value[l].progress=e,a.value[l].status=e<30?"连接服务器...":e<70?"上传中...":e<100?"处理中...":"上传完成",await new Promise(e=>setTimeout(e,200));t.value.unshift({fileName:l,fileSize:e.size,uploadTime:(new Date).toLocaleString(),status:"成功"}),delete a.value[l]}catch(o){a.value[l].status="上传失败",t.value.unshift({fileName:l,fileSize:e.size,uploadTime:(new Date).toLocaleString(),status:"失败"}),console.error("上传失败:",o)}},s=e=>{console.log("File uploaded:",e)},u=e=>{console.log("Upload progress:",e)},n=e=>{console.log("Before upload:",e)},f=e=>{t.value.splice(e,1)},p=e=>{if(0===e)return"0 Bytes";const a=1024,t=["Bytes","KB","MB","GB"],l=Math.floor(Math.log(e)/Math.log(a));return parseFloat((e/Math.pow(a,l)).toFixed(2))+" "+t[l]};return(e,i)=>((0,o.uX)(),(0,o.CE)("div",G,[(0,o.bF)((0,r.R1)(k.A),null,{header:(0,o.k6)(()=>i[0]||(i[0]=[(0,o.Lk)("div",{class:"card-header"},[(0,o.Lk)("span",{class:"card-title"},"文件上传")],-1)])),content:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(P.A),{name:"demo[]",url:"/api/upload",onUpload:s,onProgress:u,onBeforeUpload:n,multiple:!0,accept:".xlsx",maxFileSize:1e7,customUpload:!0,onUploader:l},{empty:(0,o.k6)(()=>i[1]||(i[1]=[(0,o.Lk)("div",{style:{"text-align":"center",padding:"2rem"}},[(0,o.Lk)("i",{class:"pi pi-cloud-upload",style:{"font-size":"3rem",color:"#ccc"}}),(0,o.Lk)("p",null,"拖拽 Excel 文件(.xlsx)到此处上传"),(0,o.Lk)("p",{style:{color:"#666","font-size":"0.9rem"}},"或点击选择文件按钮")],-1)])),content:(0,o.k6)(({files:e,removeFileCallback:t})=>[e.length>0?((0,o.uX)(),(0,o.CE)("div",H,[i[3]||(i[3]=(0,o.Lk)("h5",null,"待上传文件",-1)),((0,o.uX)(!0),(0,o.CE)(o.FK,null,(0,o.pI)(e,(e,l)=>((0,o.uX)(),(0,o.CE)("div",{key:e.name+e.type+e.size,class:"file-item"},[(0,o.Lk)("div",J,[i[2]||(i[2]=(0,o.Lk)("i",{class:"pi pi-file-excel",style:{"font-size":"2rem",color:"#217346"}},null,-1)),(0,o.Lk)("div",Z,[(0,o.Lk)("span",$,(0,c.v_)(e.name),1),(0,o.Lk)("span",ee,(0,c.v_)(p(e.size)),1),a.value[e.name]?((0,o.uX)(),(0,o.CE)("div",ae,[(0,o.bF)((0,r.R1)(T.A),{value:a.value[e.name].progress,showValue:!0,style:{"margin-top":"0.5rem",height:"1.2rem"}},{value:(0,o.k6)(e=>[(0,o.Lk)("span",te,(0,c.v_)(e.value)+"%",1)]),_:2},1032,["value"]),(0,o.Lk)("span",le,(0,c.v_)(a.value[e.name].status),1)])):(0,o.Q3)("",!0)])]),(0,o.bF)((0,r.R1)(d.A),{icon:"pi pi-times",onClick:e=>t(l),class:"p-button-outlined p-button-rounded p-button-danger",disabled:a.value[e.name]?.uploading},null,8,["onClick","disabled"])]))),128))])):(0,o.Q3)("",!0)]),_:1}),t.value.length>0?((0,o.uX)(),(0,o.CE)("div",oe,[i[5]||(i[5]=(0,o.Lk)("h5",null,"上传记录",-1)),(0,o.bF)((0,r.R1)(E.A),{value:t.value,stripedRows:"",tableStyle:"min-width: 50rem"},{default:(0,o.k6)(()=>[(0,o.bF)((0,r.R1)(g.A),{field:"fileName",header:"文件名"},{body:(0,o.k6)(e=>[(0,o.Lk)("div",re,[i[4]||(i[4]=(0,o.Lk)("i",{class:"pi pi-file-excel",style:{color:"#217346"}},null,-1)),(0,o.eW)(" "+(0,c.v_)(e.data.fileName),1)])]),_:1}),(0,o.bF)((0,r.R1)(g.A),{field:"fileSize",header:"文件大小"},{body:(0,o.k6)(e=>[(0,o.eW)((0,c.v_)(p(e.data.fileSize)),1)]),_:1}),(0,o.bF)((0,r.R1)(g.A),{field:"uploadTime",header:"上传时间"}),(0,o.bF)((0,r.R1)(g.A),{field:"status",header:"状态"},{body:(0,o.k6)(e=>[(0,o.bF)((0,r.R1)(j.A),{value:e.data.status,severity:"成功"===e.data.status?"success":"danger"},null,8,["value","severity"])]),_:1}),(0,o.bF)((0,r.R1)(g.A),{header:"操作"},{body:(0,o.k6)(e=>[(0,o.bF)((0,r.R1)(d.A),{icon:"pi pi-trash",onClick:a=>f(e.index),class:"p-button-outlined p-button-rounded p-button-danger p-button-sm"},null,8,["onClick"])]),_:1})]),_:1},8,["value"])])):(0,o.Q3)("",!0)]),_:1})]))}};const ie=de;var se=ie;const ue={class:"main-page-container"},ne={class:"left"},fe={class:"right"};var ce={__name:"HomePage",setup(e){const a=(0,r.KR)("home");return(0,o.Gt)("mainOperationArea_Title","业务统计数据看板"),(0,o.Gt)("sideBar_Function_Activated",a),(e,t)=>((0,o.uX)(),(0,o.CE)("div",ue,[(0,o.Lk)("div",ne,[(0,o.bF)(f)]),(0,o.Lk)("div",fe,["home"===a.value?((0,o.uX)(),(0,o.Wv)(R,{key:0})):(0,o.Q3)("",!0),"home"===a.value?((0,o.uX)(),(0,o.Wv)(B,{key:1})):(0,o.Q3)("",!0),"home"===a.value?((0,o.uX)(),(0,o.Wv)(Q,{key:2})):(0,o.Q3)("",!0),"upload"===a.value?((0,o.uX)(),(0,o.Wv)(se,{key:3})):(0,o.Q3)("",!0)])]))}};const pe=ce;var Fe=pe,ve=t(7440),be=t(2315),me=t(872);const he=(0,l.Ef)(Fe);he.use(ve.Ay,{theme:{preset:be.A,options:{prefix:"p",darkModeSelector:"system",cssLayer:!0}}}),he.use(me.A),he.mount("#app")}},a={};function t(l){var o=a[l];if(void 0!==o)return o.exports;var r=a[l]={exports:{}};return e[l].call(r.exports,r,r.exports,t),r.exports}t.m=e,function(){var e=[];t.O=function(a,l,o,r){if(!l){var d=1/0;for(n=0;n<e.length;n++){l=e[n][0],o=e[n][1],r=e[n][2];for(var i=!0,s=0;s<l.length;s++)(!1&r||d>=r)&&Object.keys(t.O).every(function(e){return t.O[e](l[s])})?l.splice(s--,1):(i=!1,r<d&&(d=r));if(i){e.splice(n--,1);var u=o();void 0!==u&&(a=u)}}return a}r=r||0;for(var n=e.length;n>0&&e[n-1][2]>r;n--)e[n]=e[n-1];e[n]=[l,o,r]}}(),function(){t.n=function(e){var a=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(a,{a:a}),a}}(),function(){t.d=function(e,a){for(var l in a)t.o(a,l)&&!t.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:a[l]})}}(),function(){t.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){t.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)}}(),function(){var e={524:0};t.O.j=function(a){return 0===e[a]};var a=function(a,l){var o,r,d=l[0],i=l[1],s=l[2],u=0;if(d.some(function(a){return 0!==e[a]})){for(o in i)t.o(i,o)&&(t.m[o]=i[o]);if(s)var n=s(t)}for(a&&a(l);u<d.length;u++)r=d[u],t.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return t.O(n)},l=self["webpackChunkbusinessdataboard"]=self["webpackChunkbusinessdataboard"]||[];l.forEach(a.bind(null,0)),l.push=a.bind(null,l.push.bind(l))}();var l=t.O(void 0,[504],function(){return t(8716)});l=t.O(l)})();
//# sourceMappingURL=app.06088717.js.map