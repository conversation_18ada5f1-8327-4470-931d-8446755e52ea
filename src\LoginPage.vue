<template>
  <div class="particles-container">
    <!-- 粒子容器组件 -->
    <Particles 
      id="tsparticles" 
      :options="particlesOptions" 
      class="particles"
    />
    
    <!-- 可选的信息叠加层 -->
    <div class="info-overlay">
      <h2>3D粒子球体效果</h2>
      <p>使用tsparticles实现的旋转球体粒子效果</p>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// 组件挂载后的调试信息
onMounted(() => {
  console.log('LoginPage 组件已挂载')
  console.log('粒子配置:', particlesOptions)

  // 检查粒子容器是否存在
  setTimeout(() => {
    const particlesElement = document.getElementById('tsparticles')
    console.log('粒子元素:', particlesElement)
    if (particlesElement) {
      console.log('粒子元素样式:', window.getComputedStyle(particlesElement))
    }
  }, 1000)
})

// 定义粒子效果的配置选项 - 简化版本用于测试
const particlesOptions = {
  background: {
    color: {
      value: "#000000" // 黑色背景
    }
  },
  fpsLimit: 60,
  interactivity: {
    events: {
      onClick: {
        enable: true,
        mode: "push"
      },
      onHover: {
        enable: true,
        mode: "repulse"
      },
      resize: true
    },
    modes: {
      push: {
        quantity: 4
      },
      repulse: {
        distance: 200,
        duration: 0.4
      }
    }
  },
  particles: {
    color: {
      value: "#ffffff"
    },
    links: {
      color: "#ffffff",
      distance: 150,
      enable: true,
      opacity: 0.5,
      width: 1
    },
    collisions: {
      enable: true
    },
    move: {
      direction: "none",
      enable: true,
      outModes: {
        default: "bounce"
      },
      random: false,
      speed: 2,
      straight: false
    },
    number: {
      density: {
        enable: true,
        area: 800
      },
      value: 80
    },
    opacity: {
      value: 0.5
    },
    shape: {
      type: "circle"
    },
    size: {
      value: { min: 1, max: 5 }
    }
  },
  detectRetina: true
};
</script>

<style scoped>
.particles-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.info-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  text-align: center;
  color: white;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

p {
  font-size: 1.2rem;
  opacity: 0.8;
}
</style>
