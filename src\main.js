import { createApp } from 'vue';
import App from './HomePage.vue';
import PrimeVue from 'primevue/config';
import Aura from '@primeuix/themes/aura';
import Particles from "@tsparticles/vue3";
import { loadFull } from 'tsparticles';

const app = createApp(App);

app.use(PrimeVue, {
    theme: {
        preset: Aura,
        options: {
            prefix: 'p',
            darkModeSelector: 'system',
            cssLayer: true
        }
    }
});

app.use(Particles,{
    init: async (engine) => {
        await loadFull(engine);
    }
});

app.mount('#app');